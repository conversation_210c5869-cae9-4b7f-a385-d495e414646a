# 🚀 Deploy Commands - Quick Reference

## Auto Deploy GitHub Pages

### Method 1: Manual Git Commands
```bash
git add .
git commit -m "Update website content"
git push origin main
```

### Method 2: Using NPM Script
```bash
npm run deploy:auto
```

### Method 3: Using Shell Script
```bash
bash scripts/deploy-test.sh
```

## Other Deploy Options

### Vercel
```bash
npm run deploy:vercel
```

### Netlify
```bash
npm run deploy:netlify
```

### Surge.sh
```bash
npm run deploy:surge
```

## Build & Test Locally

```bash
# Build for production
npm run build

# Preview build locally
npm run preview

# Development server
npm run dev
```

## Monitoring

- **GitHub Actions**: https://github.com/[username]/[repo]/actions
- **Live Website**: https://[username].github.io/[repo]
- **Build Status**: Check Actions tab for deploy progress

## Notes

- ⏱️ **Deploy time**: ~5 minutes from push to live
- 🔄 **Auto trigger**: Every push to `main` branch
- 📧 **Notifications**: GitHub sends email on deploy failure
- 🌐 **URL**: https://haitranwang.github.io/fwa

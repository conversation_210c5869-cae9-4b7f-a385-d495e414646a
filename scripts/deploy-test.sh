#!/bin/bash

# 🚀 Script test deploy GitHub Pages
# <PERSON>ạy script này để test build và commit

echo "🔧 Testing build..."

# Test build
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    
    echo "📝 Committing changes..."
    
    # Add all changes
    git add .
    
    # Commit with timestamp
    git commit -m "Auto deploy: $(date '+%Y-%m-%d %H:%M:%S')"
    
    echo "🚀 Pushing to GitHub..."
    
    # Push to main branch
    git push origin main
    
    echo "✅ Deploy triggered!"
    echo "🌐 Website will be updated in ~5 minutes"
    echo "📊 Check progress: https://github.com/$(git config --get remote.origin.url | sed 's/.*github.com[:/]\([^/]*\/[^/]*\).*/\1/' | sed 's/.git$//')/actions"
    
else
    echo "❌ Build failed! Please fix errors before deploying."
    exit 1
fi
